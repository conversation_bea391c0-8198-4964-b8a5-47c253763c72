"""
图片转PDF软件授权系统
包含授权码生成、验证、时间检查等核心功能
"""

import hashlib
import hmac
import json
import os
import platform
import socket
import subprocess
import time
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any
import requests
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64


class LicenseSystem:
    """授权系统核心类"""
    
    def __init__(self):
        # 主密钥（实际部署时应该更复杂）
        self.master_key = b"ImageToPDF_License_System_2024_SecretKey"
        self.license_file = "license.dat"
        self.time_servers = [
            "pool.ntp.org",
            "time.nist.gov", 
            "time.windows.com",
            "time.google.com"
        ]
        
    def get_machine_code(self) -> str:
        """获取机器码（基于硬件信息）"""
        try:
            # 获取CPU信息
            cpu_info = platform.processor()
            
            # 获取MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            
            # 获取主板序列号（Windows）
            motherboard_id = ""
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'], 
                                          capture_output=True, text=True)
                    motherboard_id = result.stdout.split('\n')[1].strip()
            except:
                pass
            
            # 组合信息生成机器码
            machine_info = f"{cpu_info}_{mac}_{motherboard_id}_{platform.system()}"
            machine_code = hashlib.sha256(machine_info.encode()).hexdigest()[:16]
            
            return machine_code.upper()
            
        except Exception as e:
            # 备用方案：使用计算机名和用户名
            fallback = f"{platform.node()}_{os.getlogin()}"
            return hashlib.sha256(fallback.encode()).hexdigest()[:16].upper()
    
    def get_network_time(self) -> Optional[datetime]:
        """从网络时间服务器获取准确时间 - 快速版本"""
        # 快速检查：只尝试一个服务器，超时时间缩短
        try:
            response = requests.get("http://worldtimeapi.org/api/timezone/Etc/UTC",
                                  timeout=2)  # 缩短超时时间
            if response.status_code == 200:
                data = response.json()
                # 移除时区信息，统一使用naive datetime
                dt_str = data['datetime'].replace('Z', '').split('+')[0].split('.')[0]
                return datetime.fromisoformat(dt_str)
        except:
            pass

        # 快速备用方案
        try:
            response = requests.head("https://www.google.com", timeout=1)  # 更短超时
            date_str = response.headers.get('Date')
            if date_str:
                from email.utils import parsedate_to_datetime
                dt = parsedate_to_datetime(date_str)
                return dt.replace(tzinfo=None)
        except:
            pass

        return None
    
    def verify_time_integrity(self) -> Tuple[bool, datetime]:
        """验证时间完整性，防止用户修改系统时间"""
        local_time = datetime.now()
        network_time = self.get_network_time()

        if network_time is None:
            # 无法获取网络时间，检查是否有历史时间记录
            last_time_file = "last_time.dat"
            try:
                if os.path.exists(last_time_file):
                    with open(last_time_file, 'r') as f:
                        last_time_str = f.read().strip()
                        last_time = datetime.fromisoformat(last_time_str)

                    # 检查本地时间是否被回调
                    if local_time < last_time:
                        print("警告: 检测到系统时间被回调")
                        return False, local_time

                # 保存当前时间
                with open(last_time_file, 'w') as f:
                    f.write(local_time.isoformat())

            except Exception as e:
                print(f"时间验证警告: {e}")

            # 离线模式：使用本地时间但给出警告
            print("警告: 无法获取网络时间，使用本地时间（离线模式）")
            return True, local_time

        # 确保时间对象类型一致
        if network_time.tzinfo is not None:
            network_time = network_time.replace(tzinfo=None)
        if local_time.tzinfo is not None:
            local_time = local_time.replace(tzinfo=None)

        # 允许5分钟的时间差异
        time_diff = abs((local_time - network_time).total_seconds())
        if time_diff > 300:  # 5分钟
            print(f"警告: 本地时间与网络时间差异过大 ({time_diff}秒)")
            # 在有网络的情况下，时间差异过大仍然允许，但记录警告
            return True, network_time

        return True, network_time
    
    def generate_license_key(self, machine_code: str, expire_days: int) -> str:
        """生成授权码"""
        # 获取当前时间
        current_time = datetime.now()
        expire_time = current_time + timedelta(days=expire_days)
        
        # 创建授权数据
        license_data = {
            "machine_code": machine_code,
            "issue_time": current_time.isoformat(),
            "expire_time": expire_time.isoformat(),
            "version": "1.0",
            "features": ["image_to_pdf", "batch_convert"]
        }
        
        # 序列化数据
        data_str = json.dumps(license_data, sort_keys=True)
        
        # 使用HMAC签名
        signature = hmac.new(
            self.master_key, 
            data_str.encode(), 
            hashlib.sha256
        ).hexdigest()
        
        # 组合数据和签名
        full_data = f"{data_str}|{signature}"
        
        # 加密
        key = self._derive_key(machine_code)
        fernet = Fernet(key)
        encrypted_data = fernet.encrypt(full_data.encode())
        
        # 转换为可读格式
        license_key = base64.b64encode(encrypted_data).decode()
        
        # 格式化为易读的授权码格式
        formatted_key = self._format_license_key(license_key)
        
        return formatted_key
    
    def verify_license_key(self, license_key: str) -> Tuple[bool, Dict[str, Any]]:
        """验证授权码"""
        try:
            # 获取当前机器码
            current_machine_code = self.get_machine_code()
            
            # 反格式化授权码
            clean_key = license_key.replace("-", "").replace(" ", "")
            
            # 解密
            key = self._derive_key(current_machine_code)
            fernet = Fernet(key)
            
            encrypted_data = base64.b64decode(clean_key)
            decrypted_data = fernet.decrypt(encrypted_data).decode()
            
            # 分离数据和签名
            data_str, signature = decrypted_data.rsplit("|", 1)
            
            # 验证签名
            expected_signature = hmac.new(
                self.master_key,
                data_str.encode(),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                return False, {"error": "签名验证失败"}
            
            # 解析授权数据
            license_data = json.loads(data_str)
            
            # 验证机器码
            if license_data["machine_code"] != current_machine_code:
                return False, {"error": "机器码不匹配"}
            
            # 验证时间
            time_valid, current_time = self.verify_time_integrity()
            if not time_valid:
                return False, {"error": "系统时间异常，请确保网络连接正常"}

            # 确保时间对象类型一致
            expire_time = datetime.fromisoformat(license_data["expire_time"])
            if expire_time.tzinfo is not None:
                expire_time = expire_time.replace(tzinfo=None)
            if current_time.tzinfo is not None:
                current_time = current_time.replace(tzinfo=None)

            if current_time > expire_time:
                return False, {"error": "授权已过期"}

            # 计算剩余天数
            remaining_days = (expire_time - current_time).days
            
            result = {
                "valid": True,
                "machine_code": license_data["machine_code"],
                "issue_time": license_data["issue_time"],
                "expire_time": license_data["expire_time"],
                "remaining_days": remaining_days,
                "features": license_data.get("features", [])
            }
            
            return True, result
            
        except Exception as e:
            return False, {"error": f"授权验证失败: {str(e)}"}
    
    def save_license(self, license_key: str) -> bool:
        """保存授权信息到本地文件"""
        try:
            # 验证授权码
            valid, data = self.verify_license_key(license_key)
            if not valid:
                return False
            
            # 加密保存
            machine_code = self.get_machine_code()
            key = self._derive_key(machine_code + "_file")
            fernet = Fernet(key)
            
            save_data = {
                "license_key": license_key,
                "save_time": datetime.now().isoformat(),
                "machine_code": machine_code
            }
            
            encrypted_file_data = fernet.encrypt(json.dumps(save_data).encode())
            
            with open(self.license_file, 'wb') as f:
                f.write(encrypted_file_data)
            
            return True
            
        except Exception:
            return False
    
    def load_license(self) -> Tuple[bool, Dict[str, Any]]:
        """从本地文件加载授权信息"""
        try:
            if not os.path.exists(self.license_file):
                return False, {"error": "未找到授权文件"}
            
            machine_code = self.get_machine_code()
            key = self._derive_key(machine_code + "_file")
            fernet = Fernet(key)
            
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = fernet.decrypt(encrypted_data)
            save_data = json.loads(decrypted_data.decode())
            
            # 验证机器码
            if save_data["machine_code"] != machine_code:
                return False, {"error": "授权文件与当前机器不匹配"}
            
            # 验证授权码
            return self.verify_license_key(save_data["license_key"])
            
        except Exception as e:
            return False, {"error": f"加载授权文件失败: {str(e)}"}
    
    def _derive_key(self, password: str) -> bytes:
        """从密码派生加密密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.master_key[:16],
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def _format_license_key(self, key: str) -> str:
        """格式化授权码为易读格式"""
        # 每4个字符一组，用-分隔
        formatted = "-".join([key[i:i+4] for i in range(0, len(key), 4)])
        return formatted


# 导入uuid模块
import uuid

if __name__ == "__main__":
    # 测试代码
    license_system = LicenseSystem()
    
    print("机器码:", license_system.get_machine_code())
    
    # 生成30天授权码
    machine_code = license_system.get_machine_code()
    license_key = license_system.generate_license_key(machine_code, 30)
    print("授权码:", license_key)
    
    # 验证授权码
    valid, result = license_system.verify_license_key(license_key)
    print("验证结果:", valid, result)
