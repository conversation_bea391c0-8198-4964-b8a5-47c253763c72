# 图片转PDF转换器

一个带授权验证的图片转PDF转换软件，支持多种图片格式，提供灵活的页面布局选项。

## 功能特点

- **多格式支持**: 支持 JPG、PNG、BMP、GIF、TIFF、WebP 等常见图片格式
- **灵活布局**: 支持适应页面、原始尺寸、多图片每页等布局模式
- **页面设置**: 支持 A4、Letter、Legal 等多种页面尺寸
- **批量处理**: 支持一次性添加多个图片文件
- **授权保护**: 内置授权验证系统，防止未授权使用
- **时间验证**: 结合网络时间服务器，防止用户修改系统时间绕过授权

## 系统要求

- Python 3.7+
- Windows 操作系统
- 网络连接（用于时间验证）

## 安装依赖

```bash
pip install Pillow reportlab cryptography requests
```

## 使用说明

### 用户端使用

1. **启动程序**
   - 双击 `start_app.bat` 或运行 `python main_app.py`

2. **首次使用授权**
   - 程序启动时会显示授权对话框
   - 复制显示的机器码发送给开发者
   - 获得授权码后输入并点击"激活授权"

3. **转换图片为PDF**
   - 点击"添加图片"选择要转换的图片文件
   - 在"转换设置"中调整页面尺寸、布局模式等参数
   - 点击"转换为PDF"选择保存位置并开始转换

4. **设置选项说明**
   - **页面尺寸**: A4、Letter、Legal 等标准页面尺寸
   - **布局模式**: 
     - `fit_page`: 适应页面（推荐）
     - `original_size`: 原始尺寸
     - `multiple_per_page`: 多图片每页
   - **页边距**: 设置页面边距（厘米）
   - **每页图片数**: 多图片模式下每页显示的图片数量

### 开发者端使用

1. **生成授权码**
   - 双击 `start_generator.bat` 或运行 `python license_generator.py`
   - 输入用户提供的机器码
   - 设置授权天数（7天、30天、365天等）
   - 点击"生成授权码"
   - 复制生成的授权码发送给用户

2. **授权码验证**
   - 可以在生成器中点击"验证授权码"测试授权码有效性

## 文件说明

### 核心模块
- `main_app.py` - 主程序GUI界面
- `license_system.py` - 授权系统核心
- `license_validator.py` - 授权验证模块
- `pdf_converter.py` - PDF转换核心功能
- `license_generator.py` - 授权码生成器

### 启动脚本
- `start_app.bat` - 启动主程序
- `start_generator.bat` - 启动授权码生成器
- `install_drag_drop.bat` - 安装拖拽支持库（可选）

### 数据文件
- `license.dat` - 本地授权文件（自动生成）

## 安全特性

1. **机器码绑定**: 授权码与特定机器硬件信息绑定
2. **时间验证**: 结合网络时间服务器验证，防止修改系统时间
3. **加密存储**: 授权信息使用强加密算法保护
4. **签名验证**: 授权码包含防篡改数字签名
5. **后台监控**: 运行时持续验证授权状态

## 授权流程

1. 用户启动软件，获取机器码
2. 用户将机器码发送给开发者
3. 开发者使用授权码生成器生成授权码
4. 用户输入授权码激活软件
5. 软件验证授权码并保存到本地
6. 后续启动时自动验证本地授权文件

## 注意事项

1. **网络连接**: 首次激活建议有网络连接，离线状态下也可正常使用
2. **机器码唯一性**: 每台机器的机器码都是唯一的
3. **授权码保密**: 请妥善保管授权码，避免泄露
4. **时间验证**: 软件会检测系统时间回调，但离线状态下仍可正常使用
5. **备份授权**: 建议备份授权码，重装系统后可重新激活
6. **拖拽功能**: 可选安装tkinterdnd2库以支持拖拽添加图片

## 故障排除

### 常见问题

1. **"软件未授权"错误**
   - 确认已正确激活授权码
   - 尝试重新启动程序
   - 检查授权是否过期

2. **"机器码不匹配"错误**
   - 确认授权码是否为当前机器生成
   - 检查硬件是否有重大变更

3. **"授权已过期"错误**
   - 联系开发者续费或重新获取授权码

4. **图片转换失败**
   - 检查图片文件是否损坏
   - 确认图片格式是否支持
   - 检查输出路径是否有写入权限

5. **拖拽功能不可用**
   - 运行 `install_drag_drop.bat` 安装拖拽支持库
   - 或使用"添加图片"按钮选择文件

6. **离线使用问题**
   - 软件支持离线使用，但首次激活建议联网
   - 离线状态下会显示时间验证警告，但不影响使用

## 技术支持

如遇到问题，请联系开发者并提供以下信息：
- 机器码
- 错误信息截图
- 操作系统版本
- Python版本

## 版本历史

- v1.0 - 初始版本
  - 基础图片转PDF功能
  - 授权验证系统
  - GUI用户界面
