"""
授权验证模块
负责在主程序中验证用户授权状态
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Callable, Dict, Any
from license_system import LicenseSystem


class LicenseValidator:
    """授权验证器"""
    
    def __init__(self, check_interval: int = 300):  # 5分钟检查一次
        self.license_system = LicenseSystem()
        self.check_interval = check_interval
        self.is_valid = False
        self.license_info = {}
        self.last_check_time = None
        self.validation_thread = None
        self.stop_validation = False
        self.validation_callbacks = []
        
    def add_validation_callback(self, callback: Callable[[bool, Dict[str, Any]], None]):
        """添加验证状态变化回调函数"""
        self.validation_callbacks.append(callback)
    
    def remove_validation_callback(self, callback: Callable[[bool, Dict[str, Any]], None]):
        """移除验证状态变化回调函数"""
        if callback in self.validation_callbacks:
            self.validation_callbacks.remove(callback)
    
    def _notify_callbacks(self, is_valid: bool, info: Dict[str, Any]):
        """通知所有回调函数"""
        for callback in self.validation_callbacks:
            try:
                callback(is_valid, info)
            except Exception as e:
                print(f"回调函数执行失败: {e}")
    
    def validate_license(self) -> tuple[bool, Dict[str, Any]]:
        """验证授权状态"""
        try:
            # 首先尝试从本地文件加载授权
            valid, result = self.license_system.load_license()
            
            if valid:
                self.is_valid = True
                self.license_info = result
                self.last_check_time = datetime.now()
                return True, result
            else:
                self.is_valid = False
                self.license_info = {"error": result.get("error", "授权验证失败")}
                return False, self.license_info
                
        except Exception as e:
            error_info = {"error": f"授权验证异常: {str(e)}"}
            self.is_valid = False
            self.license_info = error_info
            return False, error_info
    
    def activate_license(self, license_key: str) -> tuple[bool, Dict[str, Any]]:
        """激活授权"""
        try:
            # 验证授权码
            valid, result = self.license_system.verify_license_key(license_key)
            
            if valid:
                # 保存授权到本地
                if self.license_system.save_license(license_key):
                    self.is_valid = True
                    self.license_info = result
                    self.last_check_time = datetime.now()
                    
                    # 通知回调函数
                    self._notify_callbacks(True, result)
                    
                    return True, result
                else:
                    error_info = {"error": "保存授权文件失败"}
                    return False, error_info
            else:
                self.is_valid = False
                self.license_info = result
                return False, result
                
        except Exception as e:
            error_info = {"error": f"激活授权失败: {str(e)}"}
            self.is_valid = False
            self.license_info = error_info
            return False, error_info
    
    def start_background_validation(self):
        """启动后台验证线程"""
        if self.validation_thread is None or not self.validation_thread.is_alive():
            self.stop_validation = False
            self.validation_thread = threading.Thread(target=self._background_validation_loop)
            self.validation_thread.daemon = True
            self.validation_thread.start()
    
    def stop_background_validation(self):
        """停止后台验证线程"""
        self.stop_validation = True
        if self.validation_thread and self.validation_thread.is_alive():
            self.validation_thread.join(timeout=1)
    
    def _background_validation_loop(self):
        """后台验证循环"""
        while not self.stop_validation:
            try:
                # 检查是否需要重新验证
                current_time = datetime.now()
                
                if (self.last_check_time is None or 
                    (current_time - self.last_check_time).total_seconds() >= self.check_interval):
                    
                    previous_status = self.is_valid
                    valid, result = self.validate_license()
                    
                    # 如果状态发生变化，通知回调函数
                    if valid != previous_status:
                        self._notify_callbacks(valid, result)
                
                # 等待一段时间再检查
                time.sleep(min(60, self.check_interval // 5))  # 最多等待1分钟
                
            except Exception as e:
                print(f"后台验证出错: {e}")
                time.sleep(60)  # 出错时等待1分钟再重试
    
    def get_license_status(self) -> Dict[str, Any]:
        """获取当前授权状态"""
        if not self.is_valid:
            return {
                "valid": False,
                "error": self.license_info.get("error", "未授权"),
                "machine_code": self.license_system.get_machine_code()
            }
        
        status = {
            "valid": True,
            "machine_code": self.license_info.get("machine_code", ""),
            "expire_time": self.license_info.get("expire_time", ""),
            "remaining_days": self.license_info.get("remaining_days", 0),
            "features": self.license_info.get("features", []),
            "last_check": self.last_check_time.isoformat() if self.last_check_time else ""
        }
        
        return status
    
    def is_feature_enabled(self, feature: str) -> bool:
        """检查指定功能是否已授权"""
        if not self.is_valid:
            return False
        
        features = self.license_info.get("features", [])
        return feature in features
    
    def get_remaining_days(self) -> int:
        """获取剩余授权天数"""
        if not self.is_valid:
            return 0
        
        return self.license_info.get("remaining_days", 0)
    
    def is_license_expiring_soon(self, days_threshold: int = 7) -> bool:
        """检查授权是否即将过期"""
        if not self.is_valid:
            return False
        
        remaining_days = self.get_remaining_days()
        return 0 < remaining_days <= days_threshold
    
    def get_machine_code(self) -> str:
        """获取当前机器码"""
        return self.license_system.get_machine_code()
    
    def clear_license(self) -> bool:
        """清除本地授权文件"""
        try:
            import os
            if os.path.exists(self.license_system.license_file):
                os.remove(self.license_system.license_file)
            
            self.is_valid = False
            self.license_info = {}
            self.last_check_time = None
            
            # 通知回调函数
            self._notify_callbacks(False, {"error": "授权已清除"})
            
            return True
            
        except Exception as e:
            print(f"清除授权文件失败: {e}")
            return False
    
    def force_revalidation(self) -> tuple[bool, Dict[str, Any]]:
        """强制重新验证授权"""
        self.last_check_time = None
        return self.validate_license()


class LicenseGuard:
    """授权保护装饰器"""

    def __init__(self, validator: LicenseValidator, required_feature: Optional[str] = None):
        self.validator = validator
        self.required_feature = required_feature

    def __call__(self, func):
        """装饰器实现"""
        def wrapper(*args, **kwargs):
            # 强制重新验证授权状态
            valid, result = self.validator.validate_license()

            # 检查基本授权状态
            if not valid:
                error_msg = result.get("error", "软件未授权，请先激活授权码")
                raise PermissionError(error_msg)

            # 检查特定功能授权
            if self.required_feature and not self.validator.is_feature_enabled(self.required_feature):
                raise PermissionError(f"功能 '{self.required_feature}' 未授权")

            # 检查授权是否过期
            if self.validator.get_remaining_days() <= 0:
                raise PermissionError("授权已过期，请续费")

            return func(*args, **kwargs)

        return wrapper


# 全局授权验证器实例
_global_validator = None

def get_global_validator() -> LicenseValidator:
    """获取全局授权验证器实例"""
    global _global_validator
    if _global_validator is None:
        _global_validator = LicenseValidator()
    return _global_validator

def require_license(feature: Optional[str] = None):
    """授权检查装饰器"""
    validator = get_global_validator()
    return LicenseGuard(validator, feature)


if __name__ == "__main__":
    # 测试代码
    validator = LicenseValidator()
    
    print("机器码:", validator.get_machine_code())
    
    # 测试授权验证
    valid, result = validator.validate_license()
    print("授权状态:", valid, result)
    
    # 测试状态获取
    status = validator.get_license_status()
    print("详细状态:", status)
    
    # 测试功能检查
    print("图片转PDF功能:", validator.is_feature_enabled("image_to_pdf"))
    print("批量转换功能:", validator.is_feature_enabled("batch_convert"))
