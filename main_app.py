"""
图片转PDF主程序
带授权验证的用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
from typing import List
from pdf_converter import PDFConverter
from license_validator import LicenseValidator, require_license
from security_utils import start_security_monitoring, stop_security_monitoring, check_runtime_environment


class ImageToPDFApp:
    """图片转PDF主程序"""
    
    def __init__(self):
        # 安全检查
        if not check_runtime_environment():
            print("检测到不安全的运行环境，程序将退出")
            sys.exit(1)

        self.root = tk.Tk()
        self.root.title("图片转PDF - 现代化转换工具")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        self.root.minsize(1000, 700)

        # 设置现代化窗口属性
        self.root.configure(bg='#0f172a')  # 深色背景

        # 设置窗口图标和属性
        try:
            self.root.state('zoomed')  # Windows最大化
        except:
            pass

        # 初始化组件
        self.converter = PDFConverter()
        self.validator = LicenseValidator()
        self.image_files = []

        # 设置样式
        self.setup_styles()

        # 检查授权状态
        self.check_license_status()

        # 设置界面
        self.setup_gui()

        # 启动后台授权检查
        self.validator.add_validation_callback(self.on_license_status_changed)
        self.validator.start_background_validation()

        # 启动安全监控
        start_security_monitoring()
    
    def setup_styles(self):
        """设置现代化界面样式"""
        style = ttk.Style()

        # 精细化深色配色方案
        self.colors = {
            'primary': '#3b82f6',      # 主蓝色
            'primary_light': '#60a5fa', # 浅蓝色
            'primary_dark': '#2563eb',  # 深蓝色
            'primary_hover': '#1d4ed8', # 悬停蓝色
            'secondary': '#6366f1',     # 紫色
            'accent': '#06b6d4',        # 青色
            'success': '#10b981',       # 成功绿色
            'warning': '#f59e0b',       # 警告橙色
            'error': '#ef4444',         # 错误红色
            'background': '#0f172a',    # 主背景
            'surface': '#1e293b',       # 表面色
            'surface_light': '#334155', # 浅表面色
            'surface_hover': '#475569', # 悬停表面色
            'border': '#475569',        # 边框色
            'border_light': '#64748b',  # 浅边框色
            'text_primary': '#f8fafc',  # 主文字
            'text_secondary': '#cbd5e1', # 次文字
            'text_muted': '#94a3b8',    # 静音文字
            'text_disabled': '#64748b', # 禁用文字
            'shadow': '#000000',        # 阴影色
            'overlay': 'rgba(15, 23, 42, 0.8)' # 遮罩层
        }

        # 精细化字体系统
        self.fonts = {
            'hero': ('Segoe UI', 32, 'bold'),      # 英雄标题 - 更大更震撼
            'heading': ('Segoe UI', 18, 'bold'),   # 主标题 - 适中清晰
            'subheading': ('Segoe UI', 14, 'bold'), # 副标题 - 层次分明
            'body': ('Segoe UI', 11),              # 正文 - 舒适阅读
            'small': ('Segoe UI', 10),             # 小字 - 精致细节
            'button': ('Segoe UI', 12, 'bold'),    # 按钮 - 突出重点
            'code': ('Consolas', 10),              # 代码字体 - 等宽清晰
            'caption': ('Segoe UI', 9),            # 说明文字
            'nav': ('Segoe UI', 11, 'normal'),     # 导航文字 - 清晰易读
            'brand': ('Segoe UI', 22, 'bold')      # 品牌标题 - 专业感
        }

        # 配置现代化样式
        style.configure('Modern.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['background'])

        style.configure('Heading.TLabel',
                       font=self.fonts['heading'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['background'])

        style.configure('Subheading.TLabel',
                       font=self.fonts['subheading'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['background'])

        style.configure('Success.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['success'],
                       background=self.colors['background'])

        style.configure('Warning.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['warning'],
                       background=self.colors['background'])

        style.configure('Error.TLabel',
                       font=self.fonts['body'],
                       foreground=self.colors['error'],
                       background=self.colors['background'])

        # 现代化按钮样式
        style.configure('Modern.TButton',
                       font=self.fonts['button'],
                       padding=(20, 10),
                       relief='flat')

        style.map('Modern.TButton',
                 background=[('active', self.colors['primary_light']),
                           ('!active', self.colors['primary'])],
                 foreground=[('active', 'white'),
                           ('!active', 'white')])

        # 现代化框架样式
        style.configure('Modern.TLabelFrame',
                       background=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.TLabelFrame.Label',
                       font=self.fonts['subheading'],
                       foreground=self.colors['text_primary'],
                       background=self.colors['surface'])
    
    def check_license_status(self):
        """检查授权状态"""
        valid, result = self.validator.validate_license()
        if not valid:
            self.show_license_dialog()
    
    def show_license_dialog(self):
        """显示授权对话框"""
        dialog = LicenseDialog(self.root, self.validator)
        self.root.wait_window(dialog.dialog)
        
        # 重新检查授权状态
        if not self.validator.is_valid:
            messagebox.showerror("错误", "软件需要有效授权才能使用")
            self.root.quit()
    
    def setup_gui(self):
        """设置超现代化主界面"""
        # 创建主容器
        self.root.configure(bg=self.colors['background'])

        # 创建主布局容器
        main_container = tk.Frame(self.root, bg=self.colors['background'])
        main_container.pack(fill='both', expand=True)

        # 创建侧边栏
        self.setup_sidebar(main_container)

        # 创建主内容区域
        self.setup_main_content(main_container)

        # 设置响应式布局
        main_container.grid_columnconfigure(1, weight=1)
        main_container.grid_rowconfigure(0, weight=1)

    def setup_sidebar(self, parent):
        """设置现代化侧边栏"""
        # 侧边栏容器
        sidebar = tk.Frame(parent, bg=self.colors['surface'], width=280)
        sidebar.grid(row=0, column=0, sticky='nsew', padx=(0, 1))
        sidebar.grid_propagate(False)

        # 侧边栏内容
        sidebar_content = tk.Frame(sidebar, bg=self.colors['surface'])
        sidebar_content.pack(fill='both', expand=True, padx=20, pady=20)

        # 应用标题区域
        self.setup_app_branding(sidebar_content)

        # 功能导航区域
        self.setup_navigation(sidebar_content)

        # 设置区域
        self.setup_sidebar_settings(sidebar_content)

        # 状态信息区域
        self.setup_sidebar_status(sidebar_content)

    def setup_app_branding(self, parent):
        """设置精美的应用品牌区域"""
        # 品牌容器 - 添加微妙的背景渐变效果
        brand_frame = tk.Frame(parent, bg=self.colors['surface'])
        brand_frame.pack(fill='x', pady=(0, 25))

        # 图标容器 - 添加圆形背景
        icon_container = tk.Frame(brand_frame, bg=self.colors['surface'])
        icon_container.pack(pady=(0, 12))

        # 应用图标背景圆圈
        icon_bg = tk.Frame(icon_container,
                          bg=self.colors['primary'],
                          width=60, height=60)
        icon_bg.pack()
        icon_bg.pack_propagate(False)

        # 应用图标
        icon_label = tk.Label(icon_bg,
                             text="📄",
                             font=('Segoe UI', 28),
                             fg='white',
                             bg=self.colors['primary'])
        icon_label.place(relx=0.5, rely=0.5, anchor='center')

        # 应用名称 - 使用品牌字体
        title_label = tk.Label(brand_frame,
                              text="PDF Converter",
                              font=self.fonts['brand'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        title_label.pack(pady=(0, 3))

        # 副标题 - 更精致的描述
        subtitle_label = tk.Label(brand_frame,
                                 text="专业图片转换工具",
                                 font=self.fonts['caption'],
                                 fg=self.colors['text_muted'],
                                 bg=self.colors['surface'])
        subtitle_label.pack()

        # 版本标签
        version_label = tk.Label(brand_frame,
                               text="v2.0",
                               font=self.fonts['caption'],
                               fg=self.colors['primary'],
                               bg=self.colors['surface'])
        version_label.pack(pady=(2, 0))

    def setup_navigation(self, parent):
        """设置导航区域"""
        # 导航标题
        nav_title = tk.Label(parent,
                            text="功能",
                            font=self.fonts['subheading'],
                            fg=self.colors['text_primary'],
                            bg=self.colors['surface'])
        nav_title.pack(anchor='w', pady=(0, 15))

        # 导航按钮
        nav_buttons = [
            ("📁", "添加图片", self.add_images),
            ("⚙️", "转换设置", self.show_settings),
            ("🚀", "开始转换", self.convert_to_pdf),
            ("🗑️", "清空列表", self.clear_images),
        ]

        for icon, text, command in nav_buttons:
            self.create_nav_button(parent, icon, text, command)

    def create_nav_button(self, parent, icon, text, command):
        """创建精美的导航按钮"""
        button_frame = tk.Frame(parent, bg=self.colors['surface'])
        button_frame.pack(fill='x', pady=1)

        # 创建按钮容器，用于实现圆角效果
        button_container = tk.Frame(button_frame, bg=self.colors['surface'])
        button_container.pack(fill='x', padx=2)

        button = tk.Button(button_container,
                          text=f"{icon}  {text}",
                          command=command,
                          font=self.fonts['nav'],
                          bg=self.colors['surface'],
                          fg=self.colors['text_primary'],
                          relief='flat',
                          bd=0,
                          padx=16,
                          pady=10,
                          anchor='w',
                          cursor='hand2')
        button.pack(fill='x')

        # 增强的悬停效果
        def on_enter(e):
            button.configure(bg=self.colors['surface_hover'],
                           fg=self.colors['text_primary'])
        def on_leave(e):
            button.configure(bg=self.colors['surface'],
                           fg=self.colors['text_primary'])
        def on_click(e):
            button.configure(bg=self.colors['primary_dark'])
            button.after(100, lambda: button.configure(bg=self.colors['surface_hover']))

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        button.bind("<Button-1>", on_click)

        return button

    def setup_sidebar_settings(self, parent):
        """设置侧边栏设置区域"""
        # 设置标题
        settings_title = tk.Label(parent,
                                 text="设置",
                                 font=self.fonts['subheading'],
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'])
        settings_title.pack(anchor='w', pady=(30, 15))

        # 设置容器
        settings_frame = tk.Frame(parent, bg=self.colors['surface_light'], relief='flat', bd=0)
        settings_frame.pack(fill='x', pady=(0, 20))

        settings_content = tk.Frame(settings_frame, bg=self.colors['surface_light'])
        settings_content.pack(fill='x', padx=15, pady=15)

        # 文件大小优化
        self.optimize_size_var = tk.BooleanVar(value=True)
        optimize_check = tk.Checkbutton(settings_content,
                                       text="文件大小优化",
                                       variable=self.optimize_size_var,
                                       font=self.fonts['body'],
                                       fg=self.colors['text_primary'],
                                       bg=self.colors['surface_light'],
                                       activebackground=self.colors['surface_light'],
                                       selectcolor=self.colors['primary'],
                                       relief='flat',
                                       bd=0)
        optimize_check.pack(anchor='w')

        # 说明文字
        desc_label = tk.Label(settings_content,
                             text="减小PDF文件大小",
                             font=self.fonts['caption'],
                             fg=self.colors['text_muted'],
                             bg=self.colors['surface_light'])
        desc_label.pack(anchor='w', padx=(20, 0))

    def setup_sidebar_status(self, parent):
        """设置侧边栏状态区域"""
        # 状态容器
        status_frame = tk.Frame(parent, bg=self.colors['surface'])
        status_frame.pack(fill='x', side='bottom')

        # 授权状态
        self.license_status_label = tk.Label(status_frame,
                                           text="",
                                           font=self.fonts['small'],
                                           bg=self.colors['surface'])
        self.license_status_label.pack(pady=10)

        # 授权管理按钮
        license_button = tk.Button(status_frame,
                                  text="🔐 授权管理",
                                  command=self.show_license_dialog,
                                  font=self.fonts['small'],
                                  bg=self.colors['surface_light'],
                                  fg=self.colors['text_secondary'],
                                  relief='flat',
                                  bd=0,
                                  padx=10,
                                  pady=8,
                                  cursor='hand2')
        license_button.pack(fill='x')

        # 更新授权状态显示
        self.update_license_status_display()

    def setup_main_content(self, parent):
        """设置主内容区域"""
        # 主内容容器
        main_content = tk.Frame(parent, bg=self.colors['background'])
        main_content.grid(row=0, column=1, sticky='nsew', padx=(1, 0))

        # 内容区域
        content_area = tk.Frame(main_content, bg=self.colors['background'])
        content_area.pack(fill='both', expand=True, padx=30, pady=30)

        # 欢迎区域
        self.setup_welcome_area(content_area)

        # 拖拽区域
        self.setup_drop_area(content_area)

        # 文件列表区域
        self.setup_file_list_area(content_area)

        # 操作区域
        self.setup_action_area(content_area)

    def setup_welcome_area(self, parent):
        """设置精美的欢迎区域"""
        welcome_frame = tk.Frame(parent, bg=self.colors['background'])
        welcome_frame.pack(fill='x', pady=(0, 25))

        # 主标题 - 增加渐变效果感
        title_label = tk.Label(welcome_frame,
                              text="拖拽图片，一键转换PDF",
                              font=self.fonts['hero'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['background'])
        title_label.pack(anchor='w')

        # 副标题 - 更详细的功能描述
        subtitle_label = tk.Label(welcome_frame,
                                 text="支持 JPG、PNG、BMP、GIF 等格式 • 智能优化文件大小 • 批量处理",
                                 font=self.fonts['body'],
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['background'])
        subtitle_label.pack(anchor='w', pady=(8, 0))

        # 特性标签
        features_frame = tk.Frame(welcome_frame, bg=self.colors['background'])
        features_frame.pack(anchor='w', pady=(12, 0))

        features = ["🚀 快速转换", "🎯 高质量", "📱 现代化界面"]
        for i, feature in enumerate(features):
            feature_label = tk.Label(features_frame,
                                   text=feature,
                                   font=self.fonts['small'],
                                   fg=self.colors['primary'],
                                   bg=self.colors['background'])
            feature_label.pack(side='left', padx=(0, 20) if i < len(features)-1 else (0, 0))

    def setup_drop_area(self, parent):
        """设置精美的拖拽区域"""
        # 拖拽容器 - 添加微妙的边框
        drop_container = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=1)
        drop_container.configure(highlightbackground=self.colors['border'], highlightthickness=1)
        drop_container.pack(fill='x', pady=(0, 25))

        # 拖拽区域
        drop_area = tk.Frame(drop_container, bg=self.colors['surface'])
        drop_area.pack(fill='x', padx=40, pady=35)

        # 拖拽图标容器 - 添加圆形背景
        icon_container = tk.Frame(drop_area, bg=self.colors['surface'])
        icon_container.pack()

        # 图标背景圆圈
        icon_bg = tk.Frame(icon_container,
                          bg=self.colors['surface_light'],
                          width=80, height=80)
        icon_bg.pack()
        icon_bg.pack_propagate(False)

        # 拖拽图标
        drop_icon = tk.Label(icon_bg,
                            text="📎",
                            font=('Segoe UI', 36),
                            fg=self.colors['primary'],
                            bg=self.colors['surface_light'])
        drop_icon.place(relx=0.5, rely=0.5, anchor='center')

        # 拖拽文字
        drop_text = tk.Label(drop_area,
                            text="拖拽图片文件到此处",
                            font=self.fonts['heading'],
                            fg=self.colors['text_primary'],
                            bg=self.colors['surface'])
        drop_text.pack(pady=(15, 8))

        # 支持格式提示
        format_text = tk.Label(drop_area,
                              text="支持 JPG、PNG、BMP、GIF、TIFF 格式",
                              font=self.fonts['small'],
                              fg=self.colors['text_muted'],
                              bg=self.colors['surface'])
        format_text.pack(pady=(0, 8))

        # 或者文字
        or_text = tk.Label(drop_area,
                          text="或者",
                          font=self.fonts['body'],
                          fg=self.colors['text_muted'],
                          bg=self.colors['surface'])
        or_text.pack(pady=8)

        # 选择文件按钮 - 增强样式
        select_button = tk.Button(drop_area,
                                 text="📁 选择文件",
                                 command=self.add_images,
                                 font=self.fonts['button'],
                                 bg=self.colors['primary'],
                                 fg='white',
                                 relief='flat',
                                 bd=0,
                                 padx=35,
                                 pady=12,
                                 cursor='hand2')
        select_button.pack(pady=(0, 5))

        # 增强的悬停效果
        def on_enter(e):
            select_button.configure(bg=self.colors['primary_light'])
        def on_leave(e):
            select_button.configure(bg=self.colors['primary'])
        def on_click(e):
            select_button.configure(bg=self.colors['primary_dark'])
            select_button.after(100, lambda: select_button.configure(bg=self.colors['primary_light']))

        select_button.bind("<Enter>", on_enter)
        select_button.bind("<Leave>", on_leave)
        select_button.bind("<Button-1>", on_click)

        # 快捷键提示
        shortcut_text = tk.Label(drop_area,
                               text="快捷键: Ctrl+O",
                               font=self.fonts['caption'],
                               fg=self.colors['text_muted'],
                               bg=self.colors['surface'])
        shortcut_text.pack(pady=(8, 0))

        # 设置拖拽功能
        self.setup_drag_drop_for_area(drop_area)

    def setup_file_list_area(self, parent):
        """设置精美的文件列表区域"""
        # 列表容器 - 添加边框
        list_container = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=1)
        list_container.configure(highlightbackground=self.colors['border'], highlightthickness=1)
        list_container.pack(fill='both', expand=True, pady=(0, 25))

        # 列表标题栏
        list_header = tk.Frame(list_container, bg=self.colors['surface_light'])
        list_header.pack(fill='x', padx=0, pady=0)

        # 标题内容
        header_content = tk.Frame(list_header, bg=self.colors['surface_light'])
        header_content.pack(fill='x', padx=25, pady=12)

        title_label = tk.Label(header_content,
                              text="📋 文件列表",
                              font=self.fonts['subheading'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface_light'])
        title_label.pack(side='left')

        # 文件计数和操作按钮
        header_right = tk.Frame(header_content, bg=self.colors['surface_light'])
        header_right.pack(side='right')

        self.file_count_label = tk.Label(header_right,
                                        text="0 个文件",
                                        font=self.fonts['small'],
                                        fg=self.colors['text_muted'],
                                        bg=self.colors['surface_light'])
        self.file_count_label.pack(side='left', padx=(0, 15))

        # 清空按钮
        clear_button = tk.Button(header_right,
                               text="🗑️",
                               command=self.clear_images,
                               font=self.fonts['small'],
                               bg=self.colors['surface_light'],
                               fg=self.colors['text_muted'],
                               relief='flat',
                               bd=0,
                               padx=8,
                               pady=4,
                               cursor='hand2')
        clear_button.pack(side='right')

        # 分隔线
        separator = tk.Frame(list_container, bg=self.colors['border'], height=1)
        separator.pack(fill='x')

        # 列表内容
        list_content = tk.Frame(list_container, bg=self.colors['surface'])
        list_content.pack(fill='both', expand=True, padx=0, pady=0)

        # 创建现代化的文件列表
        self.setup_modern_file_list(list_content)

    def setup_modern_file_list(self, parent):
        """设置现代化文件列表"""
        # 列表框架
        list_frame = tk.Frame(parent, bg=self.colors['surface'])
        list_frame.pack(fill='both', expand=True)

        # 自定义Treeview样式
        style = ttk.Style()
        style.theme_use('clam')

        # 配置现代化样式
        style.configure("Modern.Treeview",
                       background=self.colors['surface_light'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['surface_light'],
                       borderwidth=0,
                       relief='flat')

        style.configure("Modern.Treeview.Heading",
                       background=self.colors['surface'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['body'],
                       borderwidth=0,
                       relief='flat')

        style.map("Modern.Treeview",
                 background=[('selected', self.colors['primary'])],
                 foreground=[('selected', 'white')])

        # 创建Treeview
        columns = ('文件名', '大小', '尺寸', '状态')
        self.image_tree = ttk.Treeview(list_frame,
                                      columns=columns,
                                      show='headings',
                                      style="Modern.Treeview",
                                      height=10)

        # 设置列
        for col in columns:
            self.image_tree.heading(col, text=col)
            if col == '文件名':
                self.image_tree.column(col, width=300, anchor='w')
            else:
                self.image_tree.column(col, width=100, anchor='center')

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.image_tree.yview)
        self.image_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.image_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def setup_action_area(self, parent):
        """设置操作区域"""
        # 操作容器
        action_container = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=0)
        action_container.pack(fill='x')

        action_content = tk.Frame(action_container, bg=self.colors['surface'])
        action_content.pack(fill='x', padx=30, pady=20)

        # 左侧：进度信息
        progress_frame = tk.Frame(action_content, bg=self.colors['surface'])
        progress_frame.pack(side='left', fill='x', expand=True)

        # 状态文字
        self.status_var = tk.StringVar(value="就绪")
        status_label = tk.Label(progress_frame,
                               textvariable=self.status_var,
                               font=self.fonts['body'],
                               fg=self.colors['text_secondary'],
                               bg=self.colors['surface'])
        status_label.pack(anchor='w')

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          maximum=100,
                                          length=300,
                                          style='Modern.Horizontal.TProgressbar')
        self.progress_bar.pack(anchor='w', pady=(5, 0))

        # 右侧：转换按钮
        self.convert_button = tk.Button(action_content,
                                       text="🚀 转换为PDF",
                                       command=self.convert_to_pdf,
                                       font=self.fonts['button'],
                                       bg=self.colors['primary'],
                                       fg='white',
                                       relief='flat',
                                       bd=0,
                                       padx=40,
                                       pady=15,
                                       cursor='hand2')
        self.convert_button.pack(side='right')

        # 转换按钮悬停效果
        def on_enter(e):
            self.convert_button.configure(bg=self.colors['primary_light'])
        def on_leave(e):
            self.convert_button.configure(bg=self.colors['primary'])

        self.convert_button.bind("<Enter>", on_enter)
        self.convert_button.bind("<Leave>", on_leave)

    def setup_drag_drop_for_area(self, area):
        """为指定区域设置拖拽功能"""
        # 这里可以添加拖拽功能的实现
        # 由于tkinter的拖拽比较复杂，这里先保留接口
        pass

    def show_settings(self):
        """显示设置对话框"""
        # 这里可以添加设置对话框
        pass

    def update_file_count(self):
        """更新文件计数显示"""
        if hasattr(self, 'file_count_label'):
            count = self.converter.get_image_count() if hasattr(self, 'converter') else 0
            self.file_count_label.config(text=f"{count} 个文件")
    
    def setup_modern_header(self, parent):
        """设置现代化标题区域"""
        # 标题卡片
        header_card = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=0)
        header_card.pack(fill='x', pady=(0, 20))

        # 添加阴影效果（通过边框模拟）
        shadow_frame = tk.Frame(parent, bg='#e2e8f0', height=2)
        shadow_frame.pack(fill='x', pady=(0, 20))

        # 标题内容
        title_frame = tk.Frame(header_card, bg=self.colors['surface'])
        title_frame.pack(fill='x', padx=30, pady=20)

        # 主标题
        title_label = tk.Label(title_frame,
                              text="图片转PDF转换器",
                              font=self.fonts['heading'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        title_label.pack(anchor='w')

        # 副标题
        subtitle_label = tk.Label(title_frame,
                                 text="简单、快速、高质量的图片转PDF工具",
                                 font=self.fonts['body'],
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['surface'])
        subtitle_label.pack(anchor='w', pady=(5, 0))

        # 授权状态区域
        status_frame = tk.Frame(title_frame, bg=self.colors['surface'])
        status_frame.pack(fill='x', pady=(15, 0))

        self.license_status_label = tk.Label(status_frame,
                                           text="",
                                           font=self.fonts['small'],
                                           bg=self.colors['surface'])
        self.license_status_label.pack(anchor='w')

        # 更新授权状态显示
        self.update_license_status_display()

    def setup_modern_cards(self, parent):
        """设置现代化功能卡片"""
        # 卡片容器
        cards_container = tk.Frame(parent, bg=self.colors['background'])
        cards_container.pack(fill='both', expand=True)

        # 设置卡片
        self.setup_settings_card(cards_container)

        # 图片管理卡片
        self.setup_image_management_card(cards_container)

        # 操作按钮卡片
        self.setup_action_card(cards_container)
    
    def setup_settings_card(self, parent):
        """设置现代化配置卡片"""
        # 设置卡片
        settings_card = self.create_modern_card(parent, "⚙️ 转换设置")

        # 设置内容
        content_frame = tk.Frame(settings_card, bg=self.colors['surface'])
        content_frame.pack(fill='x', padx=30, pady=(0, 20))

        # 文件大小优化选项
        option_frame = tk.Frame(content_frame, bg=self.colors['surface'])
        option_frame.pack(fill='x', pady=10)

        self.optimize_size_var = tk.BooleanVar(value=True)

        # 现代化复选框
        checkbox_frame = tk.Frame(option_frame, bg=self.colors['surface'])
        checkbox_frame.pack(anchor='w')

        optimize_check = tk.Checkbutton(checkbox_frame,
                                       text="启用文件大小优化",
                                       variable=self.optimize_size_var,
                                       font=self.fonts['body'],
                                       fg=self.colors['text_primary'],
                                       bg=self.colors['surface'],
                                       activebackground=self.colors['surface'],
                                       selectcolor=self.colors['primary'],
                                       relief='flat',
                                       bd=0)
        optimize_check.pack(anchor='w')

        # 说明文字
        desc_label = tk.Label(checkbox_frame,
                             text="减小PDF内图片大小，适合网络传输和存储",
                             font=self.fonts['small'],
                             fg=self.colors['text_secondary'],
                             bg=self.colors['surface'])
        desc_label.pack(anchor='w', padx=(25, 0))

        # 智能设置说明
        info_frame = tk.Frame(content_frame, bg='#f1f5f9', relief='flat', bd=1)
        info_frame.pack(fill='x', pady=(15, 0))

        info_label = tk.Label(info_frame,
                             text="💡 智能设置：自动选择最佳页面尺寸、布局和压缩参数",
                             font=self.fonts['small'],
                             fg=self.colors['text_secondary'],
                             bg='#f1f5f9')
        info_label.pack(pady=10)

        # 初始化智能设置
        self._setup_smart_settings()

    def create_modern_card(self, parent, title):
        """创建现代化卡片"""
        # 卡片容器
        card_container = tk.Frame(parent, bg=self.colors['background'])
        card_container.pack(fill='x', pady=(0, 20))

        # 卡片主体
        card = tk.Frame(card_container, bg=self.colors['surface'], relief='flat', bd=0)
        card.pack(fill='x')

        # 卡片阴影（通过多层边框模拟）
        shadow1 = tk.Frame(card_container, bg='#e2e8f0', height=1)
        shadow1.pack(fill='x')
        shadow2 = tk.Frame(card_container, bg='#f1f5f9', height=1)
        shadow2.pack(fill='x')

        # 卡片标题
        title_frame = tk.Frame(card, bg=self.colors['surface'])
        title_frame.pack(fill='x', padx=30, pady=(20, 10))

        title_label = tk.Label(title_frame,
                              text=title,
                              font=self.fonts['subheading'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        title_label.pack(anchor='w')

        return card

    def setup_image_management_card(self, parent):
        """设置现代化图片管理卡片"""
        # 图片管理卡片
        image_card = self.create_modern_card(parent, "📁 图片管理")

        # 图片列表内容
        content_frame = tk.Frame(image_card, bg=self.colors['surface'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 20))

        # 拖拽提示区域
        drop_zone = tk.Frame(content_frame, bg='#f8fafc', relief='solid', bd=1)
        drop_zone.pack(fill='x', pady=(0, 15))

        drop_label = tk.Label(drop_zone,
                             text="📎 拖拽图片文件到此处，或点击下方按钮添加",
                             font=self.fonts['body'],
                             fg=self.colors['text_secondary'],
                             bg='#f8fafc')
        drop_label.pack(pady=20)

        # 图片列表
        list_frame = tk.Frame(content_frame, bg=self.colors['surface'])
        list_frame.pack(fill='both', expand=True)

        # 创建现代化的Treeview
        style = ttk.Style()
        style.configure("Modern.Treeview",
                       background=self.colors['surface'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')
        style.configure("Modern.Treeview.Heading",
                       background=self.colors['background'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['body'])

        # 图片列表树形视图
        columns = ('文件名', '大小', '尺寸', '状态')
        self.image_tree = ttk.Treeview(list_frame, columns=columns, show='headings',
                                      style="Modern.Treeview", height=8)

        # 设置列标题
        for col in columns:
            self.image_tree.heading(col, text=col)
            self.image_tree.column(col, width=150)

        # 滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.image_tree.yview)
        self.image_tree.configure(yscrollcommand=tree_scroll.set)

        # 布局
        self.image_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # 设置拖拽功能
        self.setup_drag_drop()

    def setup_action_card(self, parent):
        """设置现代化操作按钮卡片"""
        # 操作卡片
        action_card = self.create_modern_card(parent, "🚀 操作")

        # 按钮容器
        button_container = tk.Frame(action_card, bg=self.colors['surface'])
        button_container.pack(fill='x', padx=30, pady=(0, 20))

        # 按钮网格
        button_grid = tk.Frame(button_container, bg=self.colors['surface'])
        button_grid.pack(anchor='center')

        # 主要操作按钮
        self.create_modern_button(button_grid, "📁 添加图片", self.add_images,
                                 row=0, col=0, style='primary')

        self.create_modern_button(button_grid, "🗑️ 清空列表", self.clear_images,
                                 row=0, col=1, style='secondary')

        # 转换按钮（突出显示）
        self.convert_button = self.create_modern_button(button_grid, "📄 转换为PDF", self.convert_to_pdf,
                                                       row=1, col=0, style='primary', span=2)

        # 授权管理按钮
        self.create_modern_button(button_grid, "🔐 授权管理", self.show_license_dialog,
                                 row=2, col=0, style='outline', span=2)

        # 进度条
        self.progress_var = tk.DoubleVar()
        progress_frame = tk.Frame(button_container, bg=self.colors['surface'])
        progress_frame.pack(fill='x', pady=(20, 0))

        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          maximum=100,
                                          style='Modern.Horizontal.TProgressbar')
        self.progress_bar.pack(fill='x')

        # 状态文字
        self.status_var = tk.StringVar(value="就绪")
        status_label = tk.Label(progress_frame,
                               textvariable=self.status_var,
                               font=self.fonts['small'],
                               fg=self.colors['text_secondary'],
                               bg=self.colors['surface'])
        status_label.pack(pady=(5, 0))

    def create_modern_button(self, parent, text, command, row, col, style='primary', span=1):
        """创建现代化按钮"""
        # 按钮样式
        if style == 'primary':
            bg_color = self.colors['primary']
            fg_color = 'white'
            hover_color = self.colors['primary_light']
        elif style == 'secondary':
            bg_color = self.colors['secondary']
            fg_color = 'white'
            hover_color = '#475569'
        elif style == 'outline':
            bg_color = self.colors['surface']
            fg_color = self.colors['primary']
            hover_color = '#f1f5f9'

        button = tk.Button(parent,
                          text=text,
                          command=command,
                          font=self.fonts['button'],
                          bg=bg_color,
                          fg=fg_color,
                          relief='flat',
                          bd=0,
                          padx=20,
                          pady=12,
                          cursor='hand2')

        # 悬停效果
        def on_enter(e):
            button.configure(bg=hover_color)
        def on_leave(e):
            button.configure(bg=bg_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

        # 布局
        if span > 1:
            button.grid(row=row, column=col, columnspan=span, padx=5, pady=5, sticky='ew')
        else:
            button.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

        # 配置网格权重
        for i in range(span):
            parent.grid_columnconfigure(col + i, weight=1)

        return button

    def setup_modern_status_bar(self, parent):
        """设置现代化状态栏"""
        # 状态栏卡片
        status_card = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=0)
        status_card.pack(fill='x', pady=(10, 0))

        # 状态栏内容
        status_content = tk.Frame(status_card, bg=self.colors['surface'])
        status_content.pack(fill='x', padx=30, pady=15)

        # 版本信息
        version_label = tk.Label(status_content,
                                text="图片转PDF转换器 v2.0 | 现代化界面版本",
                                font=self.fonts['small'],
                                fg=self.colors['text_secondary'],
                                bg=self.colors['surface'])
        version_label.pack(side='left')

        # 授权状态（右侧）
        self.status_right_label = tk.Label(status_content,
                                          text="",
                                          font=self.fonts['small'],
                                          bg=self.colors['surface'])
        self.status_right_label.pack(side='right')

    def setup_image_list_area(self, parent):
        """设置图片列表区域"""
        list_frame = ttk.LabelFrame(parent, text="图片列表", padding="10")
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ('文件名', '尺寸', '格式', '路径')
        self.image_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        for col in columns:
            self.image_tree.heading(col, text=col)
            
        # 设置列宽
        self.image_tree.column('文件名', width=200)
        self.image_tree.column('尺寸', width=100)
        self.image_tree.column('格式', width=80)
        self.image_tree.column('路径', width=300)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.image_tree.yview)
        self.image_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.image_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定右键菜单
        self.setup_context_menu()

        # 设置拖拽功能
        self.setup_drag_drop()
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="移除", command=self.remove_selected_image)
        self.context_menu.add_command(label="上移", command=self.move_image_up)
        self.context_menu.add_command(label="下移", command=self.move_image_down)
        
        self.image_tree.bind("<Button-3>", self.show_context_menu)
    
    def setup_action_buttons(self, parent):
        """设置操作按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 添加图片按钮
        ttk.Button(button_frame, text="添加图片", 
                  command=self.add_images).grid(row=0, column=0, padx=(0, 10))
        
        # 清空列表按钮
        ttk.Button(button_frame, text="清空列表", 
                  command=self.clear_images).grid(row=0, column=1, padx=(0, 10))
        
        # 转换按钮
        self.convert_button = ttk.Button(button_frame, text="转换为PDF", 
                                        command=self.convert_to_pdf)
        self.convert_button.grid(row=0, column=2, padx=(0, 10))
        
        # 授权管理按钮
        ttk.Button(button_frame, text="授权管理", 
                  command=self.show_license_dialog).grid(row=0, column=3, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(button_frame, variable=self.progress_var, 
                                          maximum=100, length=200)
        self.progress_bar.grid(row=0, column=4, padx=(20, 0), sticky=tk.E)
    
    def setup_status_bar(self, parent):
        """设置状态栏"""
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(parent, textvariable=self.status_var, 
                                style='Status.TLabel')
        status_label.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E))
    
    def _setup_smart_settings(self):
        """设置智能转换参数"""
        # 智能设置：A4页面，适应页面模式，1cm边距，每页1张图片
        self.converter.set_page_size("A4")
        self.converter.set_layout_mode("fit_page")
        self.converter.set_margin(1.0)
        self.converter.set_images_per_page(1)

        # 设置优化参数
        self.converter.set_optimization_enabled(True)

    def on_settings_changed(self, event=None):
        """设置改变时的回调 - 现在只处理优化选项"""
        try:
            # 更新优化设置
            self.converter.set_optimization_enabled(self.optimize_size_var.get())
        except Exception:
            pass  # 忽略错误
    
    def add_images(self):
        """添加图片文件"""
        # 检查授权状态
        if not self._check_license_for_operation():
            return

        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff *.tif *.webp"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择图片文件",
            filetypes=file_types
        )

        if files:
            self._add_image_files(files)

    def _check_license_for_operation(self) -> bool:
        """检查操作授权"""
        try:
            # 强制重新验证授权
            valid, result = self.validator.validate_license()
            if not valid:
                error_msg = result.get("error", "软件未授权，请先激活授权码")
                messagebox.showerror("授权错误", error_msg)
                return False

            # 检查功能授权
            if not self.validator.is_feature_enabled("image_to_pdf"):
                messagebox.showerror("授权错误", "图片转PDF功能未授权")
                return False

            # 检查是否过期
            if self.validator.get_remaining_days() <= 0:
                messagebox.showerror("授权错误", "授权已过期，请续费")
                return False

            return True

        except Exception as e:
            messagebox.showerror("授权错误", f"授权验证失败: {str(e)}")
            return False

    def _add_image_files(self, files):
        """添加图片文件的内部方法"""
        success_count, fail_count = self.converter.add_images(files)
        self.refresh_image_list()

        if fail_count > 0:
            messagebox.showwarning("警告",
                                 f"成功添加 {success_count} 个文件，{fail_count} 个文件添加失败")
        else:
            self.status_var.set(f"已添加 {success_count} 个图片文件")

    def setup_drag_drop(self):
        """设置拖拽功能"""
        def on_drop(event):
            # 检查授权
            if not self._check_license_for_operation():
                return

            # 获取拖拽的文件
            files = []
            try:
                # 处理拖拽数据
                data = event.data
                if data:
                    # 分割文件路径
                    file_paths = data.split()
                    for path in file_paths:
                        # 清理路径（移除大括号等）
                        clean_path = path.strip('{}')
                        if os.path.exists(clean_path):
                            # 检查是否为图片文件
                            ext = os.path.splitext(clean_path)[1].lower()
                            if ext in self.converter.SUPPORTED_FORMATS:
                                files.append(clean_path)

                if files:
                    self._add_image_files(files)
                else:
                    messagebox.showinfo("提示", "未找到支持的图片文件")

            except Exception as e:
                messagebox.showerror("错误", f"拖拽处理失败: {str(e)}")

        # 绑定拖拽事件（简化版本，使用tkinter的基本功能）
        def on_drag_enter(event):
            return "copy"

        def on_drag_motion(event):
            return "copy"

        # 为图片列表区域绑定拖拽事件
        try:
            # 尝试使用tkinterdnd2（如果可用）
            import tkinterdnd2 as tkdnd
            self.image_tree.drop_target_register(tkdnd.DND_FILES)
            self.image_tree.dnd_bind('<<Drop>>', on_drop)
        except ImportError:
            # 如果没有tkinterdnd2，使用基本的文件选择提示
            def show_drag_tip(event):
                messagebox.showinfo("提示", "请使用'添加图片'按钮选择文件，或安装tkinterdnd2库以支持拖拽功能")

            self.image_tree.bind('<Button-1>', show_drag_tip)
    
    def clear_images(self):
        """清空图片列表"""
        self.converter.clear_images()
        self.refresh_image_list()
        self.status_var.set("已清空图片列表")
    
    def refresh_image_list(self):
        """刷新图片列表显示"""
        # 清空现有项目
        for item in self.image_tree.get_children():
            self.image_tree.delete(item)
        
        # 添加新项目
        for i in range(self.converter.get_image_count()):
            image_info = self.converter.get_image_info(i)
            if image_info:
                filename = os.path.basename(image_info['path'])
                size_str = f"{image_info['size'][0]}x{image_info['size'][1]}"
                format_str = image_info['format'] or 'Unknown'
                
                self.image_tree.insert('', 'end', values=(
                    filename, size_str, format_str, image_info['path']
                ))
    
    def convert_to_pdf(self):
        """转换为PDF"""
        # 检查授权状态
        if not self._check_license_for_operation():
            return

        if self.converter.get_image_count() == 0:
            messagebox.showwarning("警告", "请先添加图片文件")
            return

        # 选择输出文件
        output_file = filedialog.asksaveasfilename(
            title="保存PDF文件",
            defaultextension=".pdf",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if output_file:
            # 在新线程中执行转换
            self.convert_button.config(state='disabled')
            self.progress_var.set(0)

            thread = threading.Thread(target=self._convert_thread, args=(output_file,))
            thread.daemon = True
            thread.start()
    
    def _convert_thread(self, output_file):
        """转换线程"""
        try:
            self.status_var.set("正在转换...")
            
            def progress_callback(progress):
                self.progress_var.set(progress)
                self.root.update_idletasks()
            
            # 应用智能设置
            self._setup_smart_settings()
            # 应用用户选择的优化设置
            self.on_settings_changed()
            
            # 执行转换
            success = self.converter.convert_to_pdf(output_file, progress_callback)
            
            if success:
                self.status_var.set(f"转换完成: {output_file}")
                messagebox.showinfo("成功", "PDF文件生成成功！")
            else:
                self.status_var.set("转换失败")
                messagebox.showerror("错误", "PDF转换失败")
                
        except Exception as e:
            self.status_var.set("转换出错")
            messagebox.showerror("错误", f"转换过程中出错: {str(e)}")
        
        finally:
            self.convert_button.config(state='normal')
            self.progress_var.set(0)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def remove_selected_image(self):
        """移除选中的图片"""
        selection = self.image_tree.selection()
        if selection:
            index = self.image_tree.index(selection[0])
            self.converter.remove_image(index)
            self.refresh_image_list()
    
    def move_image_up(self):
        """上移图片"""
        selection = self.image_tree.selection()
        if selection:
            index = self.image_tree.index(selection[0])
            if index > 0:
                self.converter.move_image(index, index - 1)
                self.refresh_image_list()
    
    def move_image_down(self):
        """下移图片"""
        selection = self.image_tree.selection()
        if selection:
            index = self.image_tree.index(selection[0])
            if index < self.converter.get_image_count() - 1:
                self.converter.move_image(index, index + 1)
                self.refresh_image_list()
    
    def update_license_status_display(self):
        """更新现代化授权状态显示"""
        if self.validator.is_valid:
            remaining_days = self.validator.get_remaining_days()
            if remaining_days > 7:
                color = self.colors['success']
                icon = "✅"
                text = f"{icon} 已授权 (剩余 {remaining_days} 天)"
            else:
                color = self.colors['warning']
                icon = "⚠️"
                text = f"{icon} 即将过期 (剩余 {remaining_days} 天)"
        else:
            color = self.colors['error']
            icon = "❌"
            text = f"{icon} 未授权"

        # 更新标题区域的授权状态
        self.license_status_label.config(text=text, fg=color)

        # 更新状态栏的授权状态
        if hasattr(self, 'status_right_label'):
            self.status_right_label.config(text=text, fg=color)
    
    def on_license_status_changed(self, is_valid, info):
        """授权状态变化回调"""
        self.root.after(0, self.update_license_status_display)
        
        if not is_valid:
            self.root.after(0, lambda: messagebox.showwarning(
                "授权警告", f"授权验证失败: {info.get('error', '未知错误')}"))
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        finally:
            self.validator.stop_background_validation()
            stop_security_monitoring()


class LicenseDialog:
    """现代化授权对话框"""

    def __init__(self, parent, validator: LicenseValidator):
        self.validator = validator
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("软件授权激活")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 现代化深色配色
        self.colors = {
            'primary': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'error': '#ef4444',
            'background': '#0f172a',
            'surface': '#1e293b',
            'surface_light': '#334155',
            'text_primary': '#f8fafc',
            'text_secondary': '#cbd5e1'
        }

        self.fonts = {
            'heading': ('Segoe UI', 16, 'bold'),
            'body': ('Segoe UI', 10),
            'small': ('Segoe UI', 9),
            'code': ('Consolas', 9)
        }

        self.dialog.configure(bg=self.colors['background'])

        self.setup_modern_dialog()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_modern_dialog(self):
        """设置现代化对话框界面"""
        # 主容器
        main_container = tk.Frame(self.dialog, bg=self.colors['background'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # 标题卡片
        title_card = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        title_card.pack(fill='x', pady=(0, 20))

        title_content = tk.Frame(title_card, bg=self.colors['surface'])
        title_content.pack(fill='x', padx=30, pady=20)

        # 标题
        title_label = tk.Label(title_content,
                              text="🔐 软件授权激活",
                              font=self.fonts['heading'],
                              fg=self.colors['text_primary'],
                              bg=self.colors['surface'])
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(title_content,
                                 text="请输入您的授权码以激活软件功能",
                                 font=self.fonts['body'],
                                 fg=self.colors['text_secondary'],
                                 bg=self.colors['surface'])
        subtitle_label.pack(anchor='w', pady=(5, 0))

        # 机器码卡片
        machine_card = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        machine_card.pack(fill='x', pady=(0, 20))

        machine_content = tk.Frame(machine_card, bg=self.colors['surface'])
        machine_content.pack(fill='x', padx=30, pady=20)

        # 机器码标题
        machine_title = tk.Label(machine_content,
                                text="📱 机器码",
                                font=self.fonts['body'],
                                fg=self.colors['text_primary'],
                                bg=self.colors['surface'])
        machine_title.pack(anchor='w')

        # 机器码显示
        machine_code = self.validator.get_machine_code()
        machine_frame = tk.Frame(machine_content, bg='#f1f5f9', relief='flat', bd=1)
        machine_frame.pack(fill='x', pady=(10, 0))

        machine_code_label = tk.Label(machine_frame,
                                     text=machine_code,
                                     font=self.fonts['code'],
                                     fg=self.colors['primary'],
                                     bg='#f1f5f9')
        machine_code_label.pack(pady=10)

        # 复制按钮
        copy_button = tk.Button(machine_content,
                               text="📋 复制机器码",
                               command=lambda: self.copy_to_clipboard(machine_code),
                               font=self.fonts['body'],
                               bg=self.colors['surface'],
                               fg=self.colors['primary'],
                               relief='flat',
                               bd=1,
                               padx=15,
                               pady=8,
                               cursor='hand2')
        copy_button.pack(anchor='w', pady=(10, 0))

        # 授权码输入卡片
        license_card = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        license_card.pack(fill='x', pady=(0, 20))

        license_content = tk.Frame(license_card, bg=self.colors['surface'])
        license_content.pack(fill='x', padx=30, pady=20)

        # 授权码标题
        license_title = tk.Label(license_content,
                                text="🔑 授权码",
                                font=self.fonts['body'],
                                fg=self.colors['text_primary'],
                                bg=self.colors['surface'])
        license_title.pack(anchor='w')

        # 授权码输入框
        self.license_key_var = tk.StringVar()
        license_entry = tk.Entry(license_content,
                                textvariable=self.license_key_var,
                                font=self.fonts['code'],
                                bg='#f8fafc',
                                fg=self.colors['text_primary'],
                                relief='flat',
                                bd=1,
                                insertbackground=self.colors['primary'])
        license_entry.pack(fill='x', pady=(10, 0), ipady=8)

        # 激活按钮
        activate_button = tk.Button(license_content,
                                   text="🚀 激活授权",
                                   command=self.activate_license,
                                   font=self.fonts['body'],
                                   bg=self.colors['primary'],
                                   fg='white',
                                   relief='flat',
                                   bd=0,
                                   padx=20,
                                   pady=12,
                                   cursor='hand2')
        activate_button.pack(pady=(15, 0))

        # 状态显示卡片
        status_card = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        status_card.pack(fill='both', expand=True)

        status_content = tk.Frame(status_card, bg=self.colors['surface'])
        status_content.pack(fill='both', expand=True, padx=30, pady=20)

        # 状态标题
        status_title = tk.Label(status_content,
                               text="📊 状态信息",
                               font=self.fonts['body'],
                               fg=self.colors['text_primary'],
                               bg=self.colors['surface'])
        status_title.pack(anchor='w')

        # 状态文本区域
        self.status_text = tk.Text(status_content,
                                  height=8,
                                  font=self.fonts['small'],
                                  bg='#f8fafc',
                                  fg=self.colors['text_primary'],
                                  relief='flat',
                                  bd=1,
                                  state='disabled',
                                  wrap='word')
        self.status_text.pack(fill='both', expand=True, pady=(10, 0))

        # 显示当前状态
        self.update_status_display()
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.dialog.clipboard_clear()
        self.dialog.clipboard_append(text)
        messagebox.showinfo("成功", "机器码已复制到剪贴板")
    
    def activate_license(self):
        """激活授权"""
        license_key = self.license_key_var.get().strip()
        if not license_key:
            messagebox.showerror("错误", "请输入授权码")
            return

        # 简化激活流程，确保界面响应
        self.status_text.config(state='normal')
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, "正在激活授权，请稍候...\n")
        self.status_text.config(state='disabled')
        self.dialog.update()

        try:
            # 执行激活
            valid, result = self.validator.activate_license(license_key)

            # 立即更新结果
            self.status_text.config(state='normal')
            self.status_text.delete(1.0, tk.END)

            if valid:
                self.status_text.insert(tk.END, "✅ 授权激活成功！\n")
                self.status_text.insert(tk.END, f"剩余天数: {result.get('remaining_days', 0)}\n")
                self.status_text.config(state='disabled')

                messagebox.showinfo("成功", "授权激活成功！")
                self.dialog.destroy()
            else:
                error_msg = result.get('error', '激活失败')
                self.status_text.insert(tk.END, f"❌ 激活失败\n")
                self.status_text.insert(tk.END, f"错误: {error_msg}\n")
                self.status_text.config(state='disabled')

                messagebox.showerror("激活失败", error_msg)

            self.update_status_display()

        except Exception as e:
            self.status_text.config(state='normal')
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(tk.END, f"❌ 激活过程中出错\n")
            self.status_text.insert(tk.END, f"错误: {str(e)}\n")
            self.status_text.config(state='disabled')

            messagebox.showerror("错误", f"激活过程中出错: {str(e)}")

    def update_status_display(self):
        """更新状态显示"""
        status = self.validator.get_license_status()
        
        self.status_text.config(state='normal')
        self.status_text.delete(1.0, tk.END)
        
        if status['valid']:
            status_info = f"""授权状态: ✓ 已激活

机器码: {status['machine_code']}
到期时间: {status['expire_time']}
剩余天数: {status['remaining_days']} 天
授权功能: {', '.join(status['features'])}
最后检查: {status['last_check']}"""
        else:
            status_info = f"""授权状态: ✗ 未激活

机器码: {status['machine_code']}
错误信息: {status.get('error', '未知错误')}

请联系开发者获取授权码"""
        
        self.status_text.insert(1.0, status_info)
        self.status_text.config(state='disabled')


if __name__ == "__main__":
    app = ImageToPDFApp()
    app.run()
