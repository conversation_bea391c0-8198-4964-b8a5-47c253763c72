"""
安全工具模块
提供反调试、反破解等安全功能
"""

import os
import sys
import time
import threading
import hashlib
import inspect
from typing import Callable, Any
import psutil


class SecurityGuard:
    """安全防护类"""
    
    def __init__(self):
        self.debug_detected = False
        self.monitoring_active = False
        self.monitor_thread = None
        
    def check_debugger(self) -> bool:
        """检测调试器"""
        try:
            # 检查是否在调试模式下运行
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
            
            # 检查常见调试器进程
            debug_processes = [
                'ollydbg.exe', 'x64dbg.exe', 'windbg.exe', 'ida.exe', 'ida64.exe',
                'cheatengine.exe', 'processhacker.exe', 'procmon.exe'
            ]
            
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in debug_processes:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except Exception:
            return False
    
    def check_virtual_machine(self) -> bool:
        """检测虚拟机环境"""
        try:
            # 检查常见虚拟机特征
            vm_indicators = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'hyper-v'
            ]
            
            # 检查系统信息
            import platform
            system_info = platform.platform().lower()
            for indicator in vm_indicators:
                if indicator in system_info:
                    return True
            
            # 检查进程名
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    for indicator in vm_indicators:
                        if indicator in proc_name:
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except Exception:
            return False
    
    def check_file_integrity(self, file_path: str, expected_hash: str = None) -> bool:
        """检查文件完整性"""
        try:
            if not os.path.exists(file_path):
                return False
            
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            if expected_hash:
                return file_hash == expected_hash
            
            # 如果没有提供期望的哈希值，只检查文件是否存在且可读
            return True
            
        except Exception:
            return False
    
    def start_monitoring(self):
        """启动安全监控"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止安全监控"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 检查调试器
                if self.check_debugger():
                    self.debug_detected = True
                    self._handle_security_violation("调试器检测")
                
                # 检查虚拟机（可选，可能误报）
                # if self.check_virtual_machine():
                #     self._handle_security_violation("虚拟机环境检测")
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception:
                pass
    
    def _handle_security_violation(self, violation_type: str):
        """处理安全违规"""
        print(f"安全警告: {violation_type}")
        # 在实际部署中，可以采取更严厉的措施，如退出程序
        # sys.exit(1)


class AntiTamper:
    """防篡改装饰器"""
    
    @staticmethod
    def protect_function(func: Callable) -> Callable:
        """保护函数不被篡改"""
        original_code = func.__code__.co_code
        original_hash = hashlib.md5(original_code).hexdigest()
        
        def wrapper(*args, **kwargs):
            # 检查函数代码是否被修改
            current_code = func.__code__.co_code
            current_hash = hashlib.md5(current_code).hexdigest()
            
            if current_hash != original_hash:
                raise RuntimeError("函数代码被篡改")
            
            return func(*args, **kwargs)
        
        return wrapper
    
    @staticmethod
    def obfuscate_string(s: str) -> str:
        """简单的字符串混淆"""
        import base64
        encoded = base64.b64encode(s.encode()).decode()
        return encoded
    
    @staticmethod
    def deobfuscate_string(s: str) -> str:
        """解混淆字符串"""
        import base64
        try:
            decoded = base64.b64decode(s.encode()).decode()
            return decoded
        except Exception:
            return s


class CodeObfuscator:
    """代码混淆工具"""
    
    @staticmethod
    def dummy_operations():
        """执行一些无意义的操作来混淆代码流程"""
        import random
        x = random.randint(1, 1000)
        y = random.randint(1, 1000)
        z = x * y + x - y
        return z % 2 == 0
    
    @staticmethod
    def fake_license_check():
        """虚假的授权检查，用于迷惑逆向工程师"""
        fake_key = "FAKE-LICENSE-KEY-12345"
        fake_hash = hashlib.md5(fake_key.encode()).hexdigest()
        return len(fake_hash) == 32
    
    @staticmethod
    def add_noise():
        """添加噪声代码"""
        import time
        start_time = time.time()
        dummy_list = [i for i in range(100)]
        dummy_sum = sum(dummy_list)
        end_time = time.time()
        return end_time - start_time < 1.0


def secure_import(module_name: str):
    """安全导入模块"""
    try:
        module = __import__(module_name)
        return module
    except ImportError:
        print(f"警告: 无法导入模块 {module_name}")
        return None


def check_runtime_environment():
    """检查运行时环境"""
    security_guard = SecurityGuard()
    
    # 检查调试器
    if security_guard.check_debugger():
        print("警告: 检测到调试器")
        return False
    
    # 检查虚拟机
    if security_guard.check_virtual_machine():
        print("警告: 检测到虚拟机环境")
        # 注意：这可能会误报，在生产环境中需要谨慎使用
    
    return True


# 全局安全守护实例
_security_guard = SecurityGuard()

def get_security_guard() -> SecurityGuard:
    """获取全局安全守护实例"""
    return _security_guard

def start_security_monitoring():
    """启动安全监控"""
    _security_guard.start_monitoring()

def stop_security_monitoring():
    """停止安全监控"""
    _security_guard.stop_monitoring()


# 装饰器：要求安全环境
def require_secure_environment(func: Callable) -> Callable:
    """要求在安全环境中运行的装饰器"""
    def wrapper(*args, **kwargs):
        if not check_runtime_environment():
            raise RuntimeError("不安全的运行环境")
        return func(*args, **kwargs)
    return wrapper


if __name__ == "__main__":
    # 测试安全功能
    print("测试安全功能...")
    
    # 检查运行环境
    if check_runtime_environment():
        print("✓ 运行环境安全")
    else:
        print("✗ 运行环境不安全")
    
    # 测试字符串混淆
    original = "这是一个测试字符串"
    obfuscated = AntiTamper.obfuscate_string(original)
    deobfuscated = AntiTamper.deobfuscate_string(obfuscated)
    
    print(f"原始字符串: {original}")
    print(f"混淆后: {obfuscated}")
    print(f"解混淆: {deobfuscated}")
    print(f"混淆测试: {'✓ 通过' if original == deobfuscated else '✗ 失败'}")
    
    # 启动安全监控
    start_security_monitoring()
    print("✓ 安全监控已启动")
    
    time.sleep(2)
    
    # 停止安全监控
    stop_security_monitoring()
    print("✓ 安全监控已停止")
