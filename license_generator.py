"""
授权码生成器
开发者使用此工具为用户生成授权码
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
from datetime import datetime, timedelta
from license_system import LicenseSystem


class LicenseGenerator:
    """授权码生成器GUI"""
    
    def __init__(self):
        self.license_system = LicenseSystem()
        self.setup_gui()
        
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("图片转PDF - 授权码生成器")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="授权码生成器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 机器码输入
        ttk.Label(main_frame, text="用户机器码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.machine_code_var = tk.StringVar()
        machine_code_entry = ttk.Entry(main_frame, textvariable=self.machine_code_var, 
                                      width=40, font=("Courier", 10))
        machine_code_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 授权天数
        ttk.Label(main_frame, text="授权天数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.days_var = tk.StringVar(value="30")
        days_frame = ttk.Frame(main_frame)
        days_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        days_entry = ttk.Entry(days_frame, textvariable=self.days_var, width=10)
        days_entry.grid(row=0, column=0, sticky=tk.W)
        
        # 预设天数按钮
        ttk.Button(days_frame, text="7天", width=6,
                  command=lambda: self.days_var.set("7")).grid(row=0, column=1, padx=5)
        ttk.Button(days_frame, text="30天", width=6,
                  command=lambda: self.days_var.set("30")).grid(row=0, column=2, padx=5)
        ttk.Button(days_frame, text="365天", width=6,
                  command=lambda: self.days_var.set("365")).grid(row=0, column=3, padx=5)
        
        # 用户信息（可选）
        ttk.Label(main_frame, text="用户信息:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.user_info_var = tk.StringVar()
        user_info_entry = ttk.Entry(main_frame, textvariable=self.user_info_var, 
                                   width=40)
        user_info_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 生成按钮
        generate_btn = ttk.Button(main_frame, text="生成授权码", 
                                 command=self.generate_license)
        generate_btn.grid(row=4, column=0, columnspan=2, pady=20)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="生成结果", padding="10")
        result_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                         pady=(10, 0))
        
        # 授权码显示
        ttk.Label(result_frame, text="授权码:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.license_key_text = scrolledtext.ScrolledText(result_frame, height=3, width=60,
                                                         font=("Courier", 9))
        self.license_key_text.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 授权信息显示
        ttk.Label(result_frame, text="授权信息:").grid(row=2, column=0, sticky=tk.W, pady=(10, 5))
        self.info_text = scrolledtext.ScrolledText(result_frame, height=8, width=60,
                                                  font=("Consolas", 9))
        self.info_text.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 操作按钮
        button_frame = ttk.Frame(result_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="复制授权码", 
                  command=self.copy_license_key).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="验证授权码", 
                  command=self.verify_license).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="清空", 
                  command=self.clear_results).grid(row=0, column=2, padx=5)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(3, weight=1)
        
        # 绑定回车键
        self.root.bind('<Return>', lambda e: self.generate_license())
        
    def generate_license(self):
        """生成授权码"""
        try:
            # 获取输入参数
            machine_code = self.machine_code_var.get().strip().upper()
            days_str = self.days_var.get().strip()
            user_info = self.user_info_var.get().strip()
            
            # 验证输入
            if not machine_code:
                messagebox.showerror("错误", "请输入用户机器码")
                return
                
            if len(machine_code) != 16:
                messagebox.showerror("错误", "机器码长度应为16位")
                return
                
            try:
                days = int(days_str)
                if days <= 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的授权天数")
                return
            
            # 生成授权码
            license_key = self.license_system.generate_license_key(machine_code, days)
            
            # 显示授权码
            self.license_key_text.delete(1.0, tk.END)
            self.license_key_text.insert(1.0, license_key)
            
            # 生成授权信息
            current_time = datetime.now()
            expire_time = current_time + timedelta(days=days)
            
            info = f"""授权信息:
机器码: {machine_code}
用户信息: {user_info if user_info else '未提供'}
生成时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}
到期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}
授权天数: {days} 天
授权码长度: {len(license_key)} 字符

注意事项:
1. 授权码与机器码绑定，只能在指定机器上使用
2. 请确保用户网络连接正常，以便进行时间验证
3. 授权码包含防篡改签名，请完整复制
4. 建议用户备份授权码，避免丢失"""
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info)
            
            messagebox.showinfo("成功", "授权码生成成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成授权码失败: {str(e)}")
    
    def verify_license(self):
        """验证生成的授权码"""
        try:
            license_key = self.license_key_text.get(1.0, tk.END).strip()
            if not license_key:
                messagebox.showerror("错误", "请先生成授权码")
                return
            
            # 验证授权码
            valid, result = self.license_system.verify_license_key(license_key)
            
            if valid:
                verify_info = f"""验证结果: ✓ 有效

详细信息:
机器码: {result['machine_code']}
签发时间: {result['issue_time']}
到期时间: {result['expire_time']}
剩余天数: {result['remaining_days']} 天
授权功能: {', '.join(result['features'])}"""
                
                messagebox.showinfo("验证成功", "授权码验证通过！")
            else:
                verify_info = f"验证结果: ✗ 无效\n\n错误信息: {result.get('error', '未知错误')}"
                messagebox.showerror("验证失败", f"授权码验证失败: {result.get('error', '未知错误')}")
            
            # 在信息区域显示验证结果
            current_info = self.info_text.get(1.0, tk.END)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, current_info + "\n" + "="*50 + "\n" + verify_info)
            
        except Exception as e:
            messagebox.showerror("错误", f"验证过程出错: {str(e)}")
    
    def copy_license_key(self):
        """复制授权码到剪贴板"""
        try:
            license_key = self.license_key_text.get(1.0, tk.END).strip()
            if not license_key:
                messagebox.showerror("错误", "没有可复制的授权码")
                return
            
            self.root.clipboard_clear()
            self.root.clipboard_append(license_key)
            messagebox.showinfo("成功", "授权码已复制到剪贴板")
            
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}")
    
    def clear_results(self):
        """清空结果"""
        self.license_key_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.machine_code_var.set("")
        self.user_info_var.set("")
        self.days_var.set("30")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    # 创建并运行授权码生成器
    generator = LicenseGenerator()
    generator.run()
