"""
图片转PDF核心功能模块
支持多种图片格式，提供灵活的页面布局选项
"""

import os
from typing import List, Tuple, Optional
from PIL import Image, ImageOps
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4, legal
from reportlab.lib.units import inch, cm
import tempfile


class PDFConverter:
    """图片转PDF转换器"""
    
    # 支持的图片格式
    SUPPORTED_FORMATS = {
        '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp'
    }
    
    # 页面尺寸选项
    PAGE_SIZES = {
        'A4': A4,
        'Letter': letter,
        'Legal': legal,
        'A3': (297*cm/10, 420*cm/10),
        'A5': (148*cm/10, 210*cm/10)
    }
    
    # 布局模式
    LAYOUT_MODES = {
        'fit_page': '适应页面',
        'original_size': '原始尺寸',
        'multiple_per_page': '多图片每页'
    }
    
    def __init__(self):
        self.images = []
        self.page_size = A4
        self.layout_mode = 'fit_page'
        self.margin = 1 * cm
        self.images_per_page = 1
        self.maintain_aspect_ratio = True
        self.auto_rotate = True
        self.optimization_enabled = True  # 默认启用优化
        self.jpeg_quality = 85  # 优化时的JPEG质量
        self.max_image_size = (1920, 1920)  # 最大图片尺寸
        
    def add_image(self, image_path: str) -> bool:
        """添加图片到转换列表"""
        try:
            if not os.path.exists(image_path):
                return False
                
            # 检查文件格式
            ext = os.path.splitext(image_path)[1].lower()
            if ext not in self.SUPPORTED_FORMATS:
                return False
            
            # 验证图片可以打开
            with Image.open(image_path) as img:
                # 获取图片信息
                image_info = {
                    'path': image_path,
                    'size': img.size,
                    'mode': img.mode,
                    'format': img.format
                }
                
            self.images.append(image_info)
            return True
            
        except Exception:
            return False
    
    def add_images(self, image_paths: List[str]) -> Tuple[int, int]:
        """批量添加图片，返回(成功数量, 失败数量)"""
        success_count = 0
        fail_count = 0
        
        for path in image_paths:
            if self.add_image(path):
                success_count += 1
            else:
                fail_count += 1
                
        return success_count, fail_count
    
    def clear_images(self):
        """清空图片列表"""
        self.images = []
    
    def set_page_size(self, size_name: str):
        """设置页面尺寸"""
        if size_name in self.PAGE_SIZES:
            self.page_size = self.PAGE_SIZES[size_name]
    
    def set_layout_mode(self, mode: str):
        """设置布局模式"""
        if mode in self.LAYOUT_MODES:
            self.layout_mode = mode
    
    def set_margin(self, margin_cm: float):
        """设置页边距（厘米）"""
        self.margin = margin_cm * cm
    
    def set_images_per_page(self, count: int):
        """设置每页图片数量"""
        if count > 0:
            self.images_per_page = count

    def set_optimization_enabled(self, enabled: bool):
        """设置是否启用文件大小优化"""
        self.optimization_enabled = enabled
        if enabled:
            self.jpeg_quality = 40  # 优化模式：更低质量，更小文件
            self.max_image_size = (1200, 1200)  # 限制更小的尺寸
        else:
            self.jpeg_quality = 95  # 高质量模式
            self.max_image_size = (3000, 3000)  # 允许更大尺寸
    
    def convert_to_pdf(self, output_path: str, progress_callback=None) -> bool:
        """转换图片为PDF"""
        try:
            if not self.images:
                return False
            
            # 创建PDF
            c = canvas.Canvas(output_path, pagesize=self.page_size)
            page_width, page_height = self.page_size
            
            total_images = len(self.images)
            processed_images = 0
            
            if self.layout_mode == 'multiple_per_page':
                # 多图片每页模式
                self._convert_multiple_per_page(c, page_width, page_height, progress_callback)
            else:
                # 单图片每页模式
                for i, image_info in enumerate(self.images):
                    self._add_image_to_page(c, image_info, page_width, page_height)

                    if i < len(self.images) - 1:  # 不是最后一张图片
                        c.showPage()

                    processed_images += 1
                    # 减少进度更新频率，提高转换速度
                    if progress_callback and (processed_images % 5 == 0 or processed_images == total_images):
                        progress = int((processed_images / total_images) * 100)
                        progress_callback(progress)
            
            c.save()
            return True
            
        except Exception as e:
            print(f"转换失败: {e}")
            return False
    
    def _convert_multiple_per_page(self, canvas_obj, page_width, page_height, progress_callback):
        """多图片每页转换模式"""
        images_per_page = self.images_per_page
        total_images = len(self.images)
        
        # 计算网格布局
        if images_per_page == 1:
            rows, cols = 1, 1
        elif images_per_page == 2:
            rows, cols = 1, 2
        elif images_per_page <= 4:
            rows, cols = 2, 2
        elif images_per_page <= 6:
            rows, cols = 2, 3
        elif images_per_page <= 9:
            rows, cols = 3, 3
        else:
            rows, cols = 4, 4
            images_per_page = 16
        
        # 计算每个图片的可用空间
        available_width = (page_width - 2 * self.margin) / cols
        available_height = (page_height - 2 * self.margin) / rows
        
        for page_start in range(0, total_images, images_per_page):
            page_images = self.images[page_start:page_start + images_per_page]
            
            for i, image_info in enumerate(page_images):
                row = i // cols
                col = i % cols
                
                # 计算图片位置
                x = self.margin + col * available_width
                y = page_height - self.margin - (row + 1) * available_height
                
                self._add_image_to_position(canvas_obj, image_info, x, y, 
                                          available_width, available_height)
            
            # 更新进度
            if progress_callback:
                processed = min(page_start + images_per_page, total_images)
                progress = int((processed / total_images) * 100)
                progress_callback(progress)
            
            # 如果不是最后一页，添加新页面
            if page_start + images_per_page < total_images:
                canvas_obj.showPage()
    
    def _add_image_to_page(self, canvas_obj, image_info, page_width, page_height):
        """添加单个图片到页面"""
        try:
            with Image.open(image_info['path']) as img:
                # 自动旋转
                if self.auto_rotate:
                    img = ImageOps.exif_transpose(img)
                
                # 转换为RGB模式（PDF兼容）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 计算图片尺寸和位置
                img_width, img_height = img.size
                
                if self.layout_mode == 'fit_page':
                    # 适应页面模式
                    available_width = page_width - 2 * self.margin
                    available_height = page_height - 2 * self.margin
                    
                    if self.maintain_aspect_ratio:
                        # 保持宽高比
                        scale_x = available_width / img_width
                        scale_y = available_height / img_height
                        scale = min(scale_x, scale_y)
                        
                        new_width = img_width * scale
                        new_height = img_height * scale
                    else:
                        # 拉伸填充
                        new_width = available_width
                        new_height = available_height
                    
                    # 居中显示
                    x = (page_width - new_width) / 2
                    y = (page_height - new_height) / 2
                    
                elif self.layout_mode == 'original_size':
                    # 原始尺寸模式
                    new_width = img_width * 72 / 96  # 转换DPI
                    new_height = img_height * 72 / 96
                    
                    # 居中显示
                    x = (page_width - new_width) / 2
                    y = (page_height - new_height) / 2
                
                # 保存临时图片文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    img.save(temp_file.name, 'JPEG', quality=95)
                    temp_path = temp_file.name
                
                # 添加图片到PDF
                canvas_obj.drawImage(temp_path, x, y, width=new_width, height=new_height)
                
                # 删除临时文件
                os.unlink(temp_path)
                
        except Exception as e:
            print(f"处理图片失败 {image_info['path']}: {e}")
    
    def _add_image_to_position(self, canvas_obj, image_info, x, y, max_width, max_height):
        """在指定位置添加图片"""
        try:
            with Image.open(image_info['path']) as img:
                # 自动旋转
                if self.auto_rotate:
                    img = ImageOps.exif_transpose(img)
                
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 计算缩放比例
                img_width, img_height = img.size
                scale_x = max_width / img_width
                scale_y = max_height / img_height
                scale = min(scale_x, scale_y)
                
                new_width = img_width * scale
                new_height = img_height * scale
                
                # 在可用空间内居中
                center_x = x + (max_width - new_width) / 2
                center_y = y + (max_height - new_height) / 2
                
                # 优化图片尺寸和质量
                if self.optimization_enabled:
                    img = self._optimize_image(img)

                # 保存临时图片文件，使用更激进的压缩
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    save_kwargs = {
                        'format': 'JPEG',
                        'quality': self.jpeg_quality,
                        'optimize': True
                    }

                    # 如果启用优化，使用更激进的压缩设置
                    if self.optimization_enabled:
                        save_kwargs.update({
                            'progressive': True,  # 渐进式JPEG
                            'subsampling': 2,     # 更激进的子采样
                        })

                    img.save(temp_file.name, **save_kwargs)
                    temp_path = temp_file.name
                
                # 添加图片到PDF
                canvas_obj.drawImage(temp_path, center_x, center_y, 
                                   width=new_width, height=new_height)
                
                # 删除临时文件
                os.unlink(temp_path)
                
        except Exception as e:
            print(f"处理图片失败 {image_info['path']}: {e}")
    
    def get_image_count(self) -> int:
        """获取图片数量"""
        return len(self.images)
    
    def get_image_info(self, index: int) -> Optional[dict]:
        """获取指定索引的图片信息"""
        if 0 <= index < len(self.images):
            return self.images[index]
        return None
    
    def remove_image(self, index: int) -> bool:
        """移除指定索引的图片"""
        if 0 <= index < len(self.images):
            self.images.pop(index)
            return True
        return False
    
    def move_image(self, from_index: int, to_index: int) -> bool:
        """移动图片位置"""
        if (0 <= from_index < len(self.images) and 
            0 <= to_index < len(self.images)):
            image = self.images.pop(from_index)
            self.images.insert(to_index, image)
            return True
        return False

    def clear_images(self):
        """清空所有图片"""
        self.images.clear()

    def _optimize_image(self, img):
        """优化图片以减小文件大小"""
        try:
            # 限制图片最大尺寸
            if img.size[0] > self.max_image_size[0] or img.size[1] > self.max_image_size[1]:
                img.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)

            # 如果是RGBA模式，转换为RGB并添加白色背景
            if img.mode == 'RGBA':
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                img = background

            return img
        except Exception:
            return img  # 如果优化失败，返回原图


if __name__ == "__main__":
    # 测试代码
    converter = PDFConverter()
    print("PDF转换器初始化完成")
    print(f"支持的格式: {converter.SUPPORTED_FORMATS}")
    print(f"页面尺寸选项: {list(converter.PAGE_SIZES.keys())}")
    print(f"布局模式: {converter.LAYOUT_MODES}")
